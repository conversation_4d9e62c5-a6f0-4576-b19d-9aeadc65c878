<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1024">
    <title data-setting="business_name">Care</title>
    <meta name="description" content="أفضل منتجات العناية بالبشرة والشعر في العراق - جودة عالية وأسعار مناسبة - توصيل مجاني - ضمان الأصالة">
    <meta name="keywords" content="منتجات العناية, البشرة, الشعر, العراق, جودة عالية, سيروم, كريم, شامبو, مكياج, عطور, توصيل مجاني">
    <meta name="author" content="Care">
    <meta name="robots" content="index, follow">
    <meta name="theme-color" content="#4a90a4">
    <meta property="og:title" content="Care - منتجات العناية بالبشرة والشعر">
    <meta property="og:description" content="أفضل منتجات العناية بالبشرة والشعر في العراق - جودة عالية وأسعار مناسبة">
    <meta property="og:type" content="website">
    <meta property="og:image" content="#">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/standardized-typography.css" rel="stylesheet">

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <style>
        /* CSS Variables */
        :root {
            /* Spacing System */
            --spacing-xs: 0.25rem;    /* 4px */
            --spacing-sm: 0.5rem;     /* 8px */
            --spacing-md: 1rem;       /* 16px */
            --spacing-lg: 1.5rem;     /* 24px */
            --spacing-xl: 2rem;       /* 32px */
            --spacing-xxl: 3rem;      /* 48px */

            /* Color System */
            --primary-color: #4a90a4;
            --primary-dark: #2c3e50;
            --primary-light: #6ba4b8;
            --text-primary: #000000;
            --text-secondary: #666666;
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --border-color: #e9ecef;

            /* Typography */
            --font-family: 'Cairo', sans-serif;
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 2rem;
            --font-size-4xl: 2.5rem;
            --font-size-5xl: 3.5rem;

            /* Shadows */
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.15);

            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #FFFFFF;
            color: #000000;
            line-height: 1.6;
            direction: rtl;
            padding-top: 80px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        /* Standardized Header Design - Clean and Simple */
        header {
            background: #121414;
            color: white;
            padding: 1.5rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .logo {
            font-size: var(--font-size-4xl);
            font-weight: 700;
            text-decoration: none;
            color: white;
            letter-spacing: 1px;
            z-index: 1002;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1rem;
            margin: 0;
            padding: 0;
        }

        .nav-menu li a {
            color: white;
            text-decoration: none;
            padding: 0.8rem 1.2rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-menu li a:hover,
        .nav-menu li a.active {
            background: var(--primary-color);
            color: white;
        }

        /* Mobile menu button - hidden by default */
        .mobile-menu-btn {
            display: none;
            z-index: 1002;
        }

        /* Cart Icon */
        .cart-icon {
            position: relative;
            cursor: pointer;
            padding: 0.8rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            color: white;
            z-index: 1002;
        }

        .cart-icon:hover {
            background: rgba(255,255,255,0.1);
        }





        /* Desktop-Only Header Styles */
        .mobile-menu-btn,
        .mobile-overlay {
            display: none !important;
        }

        .cart-icon i {
            font-size: var(--font-size-2xl);
        }

        .cart-count {
            position: absolute;
            top: 0;
            right: 0;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xs);
            font-weight: 600;
        }



        /* Hero Section */
        .hero-section {
            position: relative;
            min-height: 100vh;
            display: flex;
            align-items: center;
            overflow: hidden;
            background: linear-gradient(135deg, #4a90a4 0%, #2c3e50 100%);
        }

        .hero-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(74, 144, 164, 0.9), rgba(44, 62, 80, 0.8)),
                        url('#') center/cover;
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 4rem;
            align-items: center;
            min-height: 80vh;
            color: white;
        }

        .hero-text h1 {
            font-size: var(--font-size-5xl);
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: 2rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }





        .brand-highlight {
            color: #f39c12;
            text-shadow: 2px 2px 4px rgba(243, 156, 18, 0.3);
        }

        .hero-text p {
            font-size: var(--font-size-xl);
            line-height: 1.6;
            opacity: 0.9;
            margin-bottom: 3rem;
            max-width: 600px;
        }

        .hero-buttons {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .cta-button {
            display: inline-flex;
            align-items: center;
            gap: 0.6rem;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 700;
            font-size: var(--font-size-base);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .cta-button.primary {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
        }

        .cta-button.primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(243, 156, 18, 0.6);
        }

        .cta-button.secondary {
            background: transparent;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .cta-button.secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
        }
        .hero-showcase {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 30px;
            padding: 2rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(20px);
            text-align: center;
            color: #2c3e50;
        }

        .showcase-image {
            width: 200px;
            height: 200px;
            border-radius: 20px;
            object-fit: cover;
            margin-bottom: 1.5rem;
        }

        .showcase-info h3 {
            font-size: var(--font-size-xl);
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .showcase-price {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .current-price {
            font-size: var(--font-size-xl);
            font-weight: 800;
            color: #4a90a4;
        }

        .original-price {
            font-size: var(--font-size-base);
            text-decoration: line-through;
            color: #7f8c8d;
        }

        /* Trust Indicators */
        .trust-indicators {
            display: flex;
            gap: 2rem;
            margin: 3rem 0;
            flex-wrap: wrap;
            justify-content: center;
        }

        .trust-item {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            padding: 1rem 1.5rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .trust-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .trust-item i {
            color: #f39c12;
            font-size: var(--font-size-xl);
        }

        .trust-item span {
            font-weight: 600;
            font-size: var(--font-size-base);
        }

        /* Section Styles */
        .section {
            padding: 8rem 0;
            position: relative;
        }

        .section-header {
            text-align: center;
            margin-bottom: 5rem;
        }

        .section-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, #4a90a4, #2c3e50);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 50px;
            font-size: var(--font-size-sm);
            font-weight: 600;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(74, 144, 164, 0.3);
        }

        .section-title {
            font-size: var(--font-size-4xl);
            font-weight: 800;
            color: #2c3e50;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .section-subtitle {
            font-size: 0.6rem;
            color: #000000;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }





        /* Products Grid */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--spacing-xl);
            margin-top: var(--spacing-xl);
        }





        .product-card {
            background: var(--bg-primary);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid var(--border-color);
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
            border-color: var(--primary-color);
        }

        .product-image {
            width: 100%;
            height: 250px;
            background: linear-gradient(135deg, var(--bg-secondary) 0%, #e9ecef 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-4xl);
            color: var(--primary-color);
            position: relative;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-card:hover .product-image img {
            transform: scale(1.05);
        }

        .product-badges {
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .product-badge {
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: var(--font-size-sm);
            font-weight: 700;
            text-align: center;
        }

        .product-badge.discount {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .product-badge.featured {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .product-info {
            padding: var(--spacing-lg);
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .product-name {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .product-price {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
            margin-top: auto;
        }

        .current-price {
            font-size: var(--font-size-xl);
            font-weight: 700;
            color: var(--primary-color);
        }

        .offer-price {
            font-size: var(--font-size-base);
            text-decoration: line-through;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .original-price {
            font-size: var(--font-size-base);
            text-decoration: line-through;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .discount-badge {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: var(--font-size-xs);
            font-weight: 600;
            margin-right: auto;
        }

        .add-to-cart {
            width: 100%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            border: none;
            padding: var(--spacing-md);
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            margin-top: auto;
        }

        .add-to-cart:hover {
            background: linear-gradient(135deg, var(--primary-dark), #1a2332);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(74, 144, 164, 0.3);
        }

        .add-to-cart:disabled {
            background: var(--text-secondary);
            cursor: not-allowed;
            transform: none;
            opacity: 0.6;
        }

        .add-to-cart:disabled:hover {
            background: var(--text-secondary);
            transform: none;
            box-shadow: none;
        }

        /* Features Grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 3rem;
            margin-top: 3rem;
        }





        .feature-card {
            text-align: center;
            padding: 3rem 2rem;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.95);
            transition: all 0.4s ease;
            border: 2px solid rgba(74, 144, 164, 0.1);
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.12);
            border-color: #4a90a4;
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 2rem;
            background: linear-gradient(135deg, #4a90a4, #2c3e50);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-3xl);
            transition: all 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1);
        }

        .feature-title {
            font-size: var(--font-size-2xl);
            font-weight: 700;
            color: #000000;
            margin-bottom: 1rem;
        }

        .feature-description {
            color: #000000;
            line-height: 1.6;
            font-size: 0.5rem ;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 2rem;
            margin-top: 3rem;
        }

        .stat-item {
            text-align: center;
            padding: 2.5rem 2rem;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            transition: all 0.3s ease;
            border: 2px solid rgba(74, 144, 164, 0.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.08);
        }

        .stat-item:hover {
            transform: translateY(-5px);
            border-color: #4a90a4;
        }

        .stat-number {
            font-size: var(--font-size-4xl);
            font-weight: 900;
            color: #4a90a4;
            display: block;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.6rem;
            color: #000000;
            font-weight: 600;
        }

        /* Loading */
        .loading {
            text-align: center;
            padding: 4rem;
            font-size: var(--font-size-xl);
            color: #4a90a4;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(74, 144, 164, 0.2);
            border-top: 4px solid #4a90a4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 2rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Footer */
        footer {
            background: #0f1111;
            color: white;
            padding: 4rem 0 2rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
            margin-bottom: 3rem;
        }





        .footer-section h3 {
            color: #FFFFFF;
            margin-bottom: 2rem;
            font-size: var(--font-size-xl);
            font-weight: 700;
        }

        .footer-section p,
        .footer-section a {
            color: #FFFFFF;
            text-decoration: none;
            line-height: 1.8;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            margin-bottom: 0.8rem;
            transition: all 0.3s ease;
        }

        .footer-section a:hover {
            color: #4a90a4;
        }

        .footer-section i {
            font-size: var(--font-size-lg);
            color: #4a90a4;
            min-width: 20px;
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .social-links a:hover {
            border-color: #4a90a4;
            background: rgba(74, 144, 164, 0.2);
        }

        .social-links a i {
            font-size: var(--font-size-xl);
            min-width: auto;
        }

        .footer-bottom {
            text-align: center;
            padding: 2rem 0;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: #FFFFFF;
        }

        .business-name {
            color: #4a90a4;
            font-weight: 700;
        }

        /* Notification System */
        .cart-notification {
            position: fixed;
            top: 100px;
            right: 20px;
            z-index: 10000;
            opacity: 0;
            transform: translateX(400px);
            transition: all 0.4s ease;
            max-width: 400px;
            width: calc(100% - 40px);
        }

        .cart-notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        .notification-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            border-left: 5px solid #4a90a4;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .notification-icon {
            background: linear-gradient(135deg, #4a90a4, #2c3e50);
            color: white;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xl);
            flex-shrink: 0;
        }

        .notification-content {
            flex: 1;
            color: #000000;
        }

        .notification-title {
            font-size: var(--font-size-base);
            font-weight: 700;
            margin-bottom: 0.3rem;
            color: #4a90a4;
        }

        .notification-message {
            font-size: var(--font-size-sm);
            color: #000000;
            line-height: 1.4;
            margin-bottom: 0.8rem;
        }

        .notification-actions {
            display: flex;
            gap: 0.8rem;
        }

        .notification-btn {
            padding: 0.4rem 0.8rem;
            border: none;
            border-radius: 6px;
            font-size: var(--font-size-sm);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
        }

        .notification-btn.primary {
            background: linear-gradient(135deg, #4a90a4, #2c3e50);
            color: white;
        }

        .notification-btn.secondary {
            background: rgba(74, 144, 164, 0.1);
            color: #4a90a4;
            border: 1px solid rgba(74, 144, 164, 0.3);
        }

        /* Accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        a:focus,
        button:focus {
            outline: 2px solid #4a90a4;
            outline-offset: 2px;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .animate-on-scroll.animated {
            opacity: 1;
            transform: translateY(0);
        }

        /* Testimonials Section */
        .testimonials-section {
            padding: 6rem 0;
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--border-color) 100%);
            position: relative;
        }

        .testimonials-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--spacing-xl);
            margin: var(--spacing-xxl) 0;
        }

        .testimonial-card {
            background: var(--bg-primary);
            padding: var(--spacing-xl);
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(74, 144, 164, 0.1);
            font-family: var(--font-family);
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;
            overflow: hidden;
        }

        .testimonial-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 60px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
            border-radius: 0 0 0 4px;
        }

        .testimonial-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-color);
        }

        .testimonial-rating {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .testimonial-rating .stars {
            display: flex;
            gap: 0.2rem;
            direction: ltr;
        }

        .testimonial-rating .stars i {
            color: #ffc107;
            font-size: var(--font-size-base);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .testimonial-card:hover .testimonial-rating .stars i {
            transform: scale(1.1);
        }

        .rating-text {
            font-weight: 600;
            color: var(--primary-color);
            font-size: var(--font-size-sm);
            font-family: var(--font-family);
        }

        .testimonial-text {
            font-size: var(--font-size-base);
            line-height: 1.6;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-lg);
            font-style: italic;
            font-family: var(--font-family);
            flex: 1;
            text-align: right;
            direction: rtl;
            position: relative;
            padding-right: 1.5rem;
        }

        .testimonial-text::before {
            content: '"';
            position: absolute;
            top: -0.5rem;
            right: 0;
            font-size: var(--font-size-4xl);
            color: rgba(74, 144, 164, 0.2);
            font-family: serif;
            line-height: 1;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: var(--font-size-xl);
            font-family: var(--font-family);
            flex-shrink: 0;
            box-shadow: 0 4px 12px rgba(74, 144, 164, 0.3);
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .testimonial-card:hover .author-avatar {
            transform: scale(1.05);
            box-shadow: 0 6px 16px rgba(74, 144, 164, 0.4);
        }

        .author-info h4 {
            margin: 0;
            font-size: var(--font-size-base);
            color: var(--primary-dark);
            font-family: var(--font-family);
            font-weight: 600;
            text-align: right;
            direction: rtl;
        }

        .author-info span {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            font-family: var(--font-family);
            text-align: right;
            direction: rtl;
        }

        .testimonials-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-xl);
            margin-top: var(--spacing-xxl);
            padding-top: var(--spacing-xxl);
            border-top: 1px solid rgba(74, 144, 164, 0.2);
        }

        .testimonials-stats .stat-item {
            text-align: center;
        }

        .testimonials-stats .stat-number {
            font-size: 0.6rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--spacing-sm);
            font-family: var(--font-family);
        }

        .testimonials-stats .stat-label {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            font-weight: 600;
            font-family: var(--font-family);
        }

        /* Timeline Section Styles */
        .timeline-section {
            background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
            padding: var(--spacing-xl) 0;
            position: relative;
            border-top: 2px solid #4a90a4;
        }

        .timeline-container {
            position: relative;
            max-width: 1000px;
            margin: 0 auto;
            padding: var(--spacing-xl) 0;
        }

        .timeline-line {
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(180deg, #4a90a4 0%, #2c3e50 100%);
            transform: translateX(-50%);
            border-radius: 2px;
            box-shadow: 0 0 10px rgba(74, 144, 164, 0.3);
        }

        .timeline-item {
            position: relative;
            margin-bottom: var(--spacing-xl);
            width: 45%;
        }

        .timeline-item-right {
            margin-left: 55%;
        }

        .timeline-marker {
            position: absolute;
            top: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #4a90a4 0%, #2c3e50 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-2xl);
            box-shadow: 0 4px 20px rgba(74, 144, 164, 0.3);
            z-index: 2;
            transition: all 0.3s ease;
        }

        .timeline-marker:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(74, 144, 164, 0.4);
        }

        .timeline-item .timeline-marker {
            right: -85px;
        }

        .timeline-item-right .timeline-marker {
            left: -85px;
        }

        .timeline-content {
            background: white;
            padding: var(--spacing-lg);
            border-radius: 15px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(74, 144, 164, 0.1);
            position: relative;
            transition: all 0.3s ease;
        }

        .timeline-content:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .timeline-content::before {
            content: '';
            position: absolute;
            top: 30px;
            width: 0;
            height: 0;
            border: 15px solid transparent;
        }

        .timeline-item .timeline-content::before {
            right: -30px;
            border-left-color: white;
        }

        .timeline-item-right .timeline-content::before {
            left: -30px;
            border-right-color: white;
        }

        .timeline-year {
            display: inline-block;
            background: linear-gradient(135deg, #4a90a4 0%, #2c3e50 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: var(--font-size-sm);
            margin-bottom: var(--spacing-sm);
            box-shadow: 0 2px 10px rgba(74, 144, 164, 0.3);
        }

        .timeline-content h3 {
            color: #000000;
            font-size: var(--font-size-2xl);
            font-weight: 700;
            margin-bottom: var(--spacing-sm);
            line-height: 1.3;
        }

        .timeline-content p {
            color: #000000;
            line-height: 1.6;
            margin: 0;
        }

        .timeline-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-xl);
            padding: var(--spacing-lg);
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            border: 2px solid #4a90a4;
            transition: all 0.3s ease;
        }

        .timeline-stats:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
            border-color: #2c3e50;
        }

        .timeline-stat {
            text-align: center;
            padding: var(--spacing-md);
            position: relative;
            transition: all 0.3s ease;
            border-radius: 15px;
            background: #ffffff;
        }

        .timeline-stat:hover {
            transform: translateY(-3px);
            background: #f8f9fa;
            border: 1px solid #4a90a4;
        }

        .timeline-stat .stat-number {
            font-size: var(--font-size-4xl);
            font-weight: 700;
            color: #4a90a4;
            margin-bottom: 0.5rem;
            display: block;
            transition: all 0.3s ease;
        }

        .timeline-stat:hover .stat-number {
            transform: scale(1.1);
            color: #2c3e50;
        }

        .timeline-stat .stat-label {
            color: #000000;
            font-weight: 600;
            font-size: var(--font-size-base);
        }

        /* Benefits Showcase Section Styles */
        .benefits-showcase-section {
            background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
            padding: var(--spacing-xl) 0;
            position: relative;
            border-top: 2px solid #4a90a4;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .benefit-item {
            background: #ffffff;
            border-radius: 20px;
            padding: var(--spacing-lg);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            border: 2px solid #4a90a4;
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
            cursor: pointer;
        }

        .benefit-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
            border-color: #2c3e50;
        }

        .benefit-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #4a90a4 0%, #2c3e50 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--spacing-md);
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(74, 144, 164, 0.3);
        }

        .benefit-icon i {
            font-size: var(--font-size-3xl);
            color: white;
        }

        .benefit-item:hover .benefit-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 6px 25px rgba(74, 144, 164, 0.4);
        }

        .benefit-content h3 {
            color: #000000;
            font-size: 0.8rem;
            font-weight: 700;
            margin-bottom: var(--spacing-sm);
            line-height: 1.3;
        }

        .benefit-content p {
            color: #000000;
            line-height: 1.6;
            margin: 0;
            font-weight: 500;
			font-size: 0.6rem;
        }

        .benefit-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #4a90a4 0%, #2c3e50 100%);
            color: white;
            padding: var(--spacing-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transform: translateY(100%);
            transition: all 0.4s ease;
        }

        .benefit-item:hover .benefit-overlay {
            opacity: 1;
            transform: translateY(0);
        }

        .benefit-details {
            text-align: center;
        }

        .benefit-details h4 {
            font-size: var(--font-size-xl);
            font-weight: 700;
            margin-bottom: var(--spacing-sm);
            color: white;
        }

        .benefit-details p {
            line-height: 1.6;
            margin-bottom: var(--spacing-md);
            opacity: 0.9;
        }

        .benefit-stats {
            display: flex;
            justify-content: center;
            gap: var(--spacing-md);
        }

        .benefit-stats .stat {
            text-align: center;
        }

        .benefit-stats .stat strong {
            display: block;
            font-size: var(--font-size-2xl);
            font-weight: 700;
            color: white;
            margin-bottom: 0.25rem;
        }

        .benefit-stats .stat small {
            font-size: var(--font-size-sm);
            opacity: 0.8;
        }

        .benefits-summary {
            background: white;
            border-radius: 20px;
            padding: var(--spacing-xl);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            border: 1px solid rgba(74, 144, 164, 0.1);
        }

        .benefits-summary h3 {
            color: #000000;
            font-size: var(--font-size-3xl);
            font-weight: 700;
            margin-bottom: var(--spacing-md);
        }

        .benefits-summary p {
            color: #000000;
            line-height: 1.6;
            margin-bottom: var(--spacing-lg);
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            font-size: 0.6rem;

        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-lg);
        }

        .summary-stat {
            text-align: center;
        }

        .summary-stat .stat-number {
            display: block;
            font-size: var(--font-size-4xl);
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .summary-stat .stat-label {
            color: #000000;
            font-weight: 600;
        }

        /* Newsletter Section */
        .newsletter-section {
            padding: 6rem 0;
            background: linear-gradient(135deg, #4a90a4 0%, #2c3e50 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .newsletter-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-50px) translateY(-50px); }
        }

        .newsletter-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .newsletter-text h2 {
            font-size: var(--font-size-4xl);
            margin-bottom: 1rem;
            font-weight: 700;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .newsletter-text p {
            font-size: var(--font-size-lg);
            margin-bottom: 2rem;
            color: white;
            line-height: 1.6;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .newsletter-benefits {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .newsletter-benefits .benefit-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: var(--font-size-base);
            color: #000000;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.95);
            padding: 0.75rem 1rem;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .newsletter-benefits .benefit-item i {
            width: 20px;
            color: #4a90a4;
            font-size: var(--font-size-xl);
            font-weight: 600;
        }

        .newsletter-form-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 2.5rem;
            border-radius: 20px;
            border: 2px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .newsletter-form .form-group {
            display: flex;
            gap: 0;
            margin-bottom: 1rem;
        }

        .newsletter-form input {
            flex: 1;
            padding: 1rem 1.5rem;
            border: 2px solid #4a90a4;
            border-radius: 50px 0 0 50px;
            font-size: var(--font-size-base);
            outline: none;
            background: white;
            color: #000000;
            font-weight: 500;
        }

        .newsletter-btn {
            padding: 0.8rem 1.5rem;
            background: #ffc107;
            color: #2c3e50;
            border: none;
            border-radius: 0 50px 50px 0;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.4rem;
            font-size: var(--font-size-sm);
        }

        .newsletter-btn:hover {
            background: #ffb300;
            transform: translateX(-2px);
        }

        .newsletter-privacy {
            text-align: center;
            font-size: var(--font-size-sm);
            background: rgba(255, 255, 255, 0.95);
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
        }

        .newsletter-privacy small {
            color: #000000;
            font-weight: 500;
            line-height: 1.4;
			font-size: 0.6rem;
        }

        .newsletter-success {
            text-align: center;
            padding: 2rem;
        }

        .newsletter-success .success-icon {
            font-size: var(--font-size-4xl);
            color: #28a745;
            margin-bottom: 1rem;
        }

        .newsletter-success h3 {
            margin-bottom: 1rem;
            font-size: var(--font-size-2xl);
        }

        .newsletter-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
            padding-top: 3rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 2;
        }

        .newsletter-stats .stat-item {
            text-align: center;
        }

        .newsletter-stats .stat-number {
            font-size: 0.6rem;
            font-weight: 700;
            color: #ffc107;
            margin-bottom: 0.5rem;
        }

        .newsletter-stats .stat-label {
            font-size: 0.6rem;
            color: #000000;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.95);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            text-shadow: none;
        }



        /* Product Categories Section */
        .categories-section {
            padding: 6rem 0;
            background: #f8f9fa;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 4rem 0;
        }

        .category-card {
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 350px;
        }

        .category-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .category-image {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .category-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .category-card:hover .category-image img {
            transform: scale(1.1);
        }

        .category-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(74, 144, 164, 0.9), rgba(44, 62, 80, 0.8));
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            color: white;
            opacity: 0;
            transition: all 0.3s ease;
            padding: 2rem;
        }

        .category-card:hover .category-overlay {
            opacity: 1;
        }

        .category-info h3 {
            font-size: var(--font-size-2xl);
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .category-info p {
            font-size: var(--font-size-base);
            margin-bottom: 1rem;
            opacity: 0.9;
            line-height: 1.5;
        }

        .category-count {
            font-size: var(--font-size-sm);
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            margin-bottom: 1.5rem;
            display: inline-block;
        }

        .category-btn {
            background: #ffc107;
            color: #2c3e50;
            padding: 0.8rem 1.5rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.4rem;
            transition: all 0.3s ease;
            transform: translateY(20px);
            font-size: var(--font-size-sm);
        }

        .category-card:hover .category-btn {
            transform: translateY(0);
        }

        .category-btn:hover {
            background: #ffb300;
            transform: translateY(-2px);
        }

        /* Alternative layout for categories without hover overlay */
        .category-card.simple {
            background: white;
            padding: 0;
            height: auto;
        }

        .category-card.simple .category-image {
            height: 200px;
        }

        .category-card.simple .category-overlay {
            position: static;
            background: none;
            opacity: 1;
            color: #333;
            padding: 1.5rem;
        }

        .category-card.simple .category-info h3 {
            color: #2c3e50;
            font-size: var(--font-size-2xl);
        }

        .category-card.simple .category-info p {
            color: #666;
            opacity: 1;
        }

        .category-card.simple .category-count {
            background: #4a90a4;
            color: white;
        }

        .category-card.simple .category-btn {
            transform: none;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="index.html" class="logo business-name">Care</a>



                <nav>
                    <ul class="nav-menu" id="navMenu">
                        <li><a href="index.html" class="active">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="offers.html">العروض</a></li>
                        <li><a href="guidelines.html">الإرشادات</a></li>
                        <li><a href="faq.html">الأسئلة الشائعة</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                    </ul>
                </nav>

                <div class="cart-icon" onclick="window.location.href='cart.html'">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count" id="cartCount">0</span>
                </div>
            </div>
        </div>


    </header>

    <main role="main">
        <!-- Hero Section -->
        <section class="hero-section" aria-label="البانر الرئيسي">
            <div class="hero-background"></div>
            <div class="container">
                <div class="hero-content">
                    <div class="hero-text">
                        <h1>
                            <span data-setting="hero_title_line1">اكتشف عالم الجمال الحقيقي</span><br>
                            <span data-setting="hero_title_line2">مع متجر <span class="brand-highlight" data-setting="business_name">Care</span></span>
                        </h1>
                        <p data-setting="hero_subtitle">منتجات عناية فاخرة من أفضل العلامات التجارية العالمية - جودة استثنائية وخدمة لا مثيل لها في جميع أنحاء العراق</p>

                        <div class="trust-indicators">
                            <div class="trust-item">
                                <i class="fas fa-certificate" data-setting="trust_indicator_1_icon"></i>
                                <span data-setting="trust_indicator_1">منتجات أصلية 100%</span>
                            </div>
                            <div class="trust-item">
                                <i class="fas fa-shipping-fast" data-setting="trust_indicator_2_icon"></i>
                                <span data-setting="trust_indicator_2">توصيل مجاني للطلبات +50 ألف</span>
                            </div>
                            <div class="trust-item">
                                <i class="fas fa-undo-alt" data-setting="trust_indicator_3_icon"></i>
                                <span data-setting="trust_indicator_3">ضمان الاسترداد 30 يوم</span>
                            </div>
                        </div>

                        <div class="hero-buttons">
                            <a href="products.html" class="cta-button primary">
                                <span>استكشف المجموعة</span>
                                <i class="fas fa-arrow-left"></i>
                            </a>
                            <a href="offers.html" class="cta-button secondary">
                                <span>العروض الحصرية</span>
                                <i class="fas fa-fire"></i>
                            </a>
                        </div>
                    </div>

                    <div class="hero-showcase">
                        <img src="#" alt="منتج مميز" class="showcase-image">
                        <div class="showcase-info">
                            <h3 data-setting="featured_product_name">سيروم فيتامين سي المتقدم</h3>
                            <div class="showcase-price">
                                <span class="current-price" data-setting="featured_product_price">45,000 د.ع</span>
                                <span class="original-price" data-setting="featured_product_original_price">60,000 د.ع</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Products Section -->
        <section class="section" aria-label="المنتجات المميزة" data-section="featured_products">
            <div class="container">
                <div class="section-header">
                    <div class="section-badge">
                        <i class="fas fa-star" aria-hidden="true"></i>
                        <span data-setting="featured_products_badge">منتجات مختارة بعناية</span>
                    </div>
                    <h2 class="section-title" data-setting="featured_products_title">المنتجات المميزة</h2>
                    <p class="section-subtitle" data-setting="featured_products_subtitle">اكتشف مجموعتنا المختارة بعناية من أفضل منتجات العناية بالبشرة والشعر من العلامات التجارية الموثوقة</p>
                </div>

                <div class="loading" id="loading" aria-live="polite">
                    <div class="loading-spinner"></div>
                    <p>جاري تحميل المنتجات المميزة...</p>
                </div>

                <div class="products-grid" id="featuredProducts" role="grid" aria-label="شبكة المنتجات المميزة"></div>

                <div style="text-align: center; margin-top: 4rem;">
                    <a href="products.html" class="cta-button primary">
                        <i class="fas fa-arrow-left"></i>
                        عرض جميع المنتجات
                    </a>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="section" style="background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);" data-section="features">
            <div class="container">
                <div class="section-header">
                    <div class="section-badge">
                        <i class="fas fa-gem"></i>
                        <span data-setting="features_badge">خدماتنا المميزة</span>
                    </div>
                    <h2 class="section-title" data-setting="features_title">لماذا تختار متجر Care؟</h2>
                    <p class="section-subtitle" data-setting="features_subtitle">نقدم لك تجربة تسوق استثنائية مع خدمات متطورة وضمانات شاملة تضمن رضاك التام</p>
                </div>

                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-certificate" data-setting="feature_1_icon"></i>
                        </div>
                        <h3 class="feature-title" data-setting="feature_1_title">منتجات أصلية مضمونة</h3>
                        <p class="feature-description" data-setting="feature_1_description">جميع منتجاتنا أصلية 100% مع ضمان الجودة والأصالة من المصدر المباشر</p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shipping-fast" data-setting="feature_2_icon"></i>
                        </div>
                        <h3 class="feature-title" data-setting="feature_2_title">توصيل سريع ومجاني</h3>
                        <p class="feature-description" data-setting="feature_2_description">توصيل مجاني لجميع أنحاء العراق للطلبات أكثر من 50 ألف دينار خلال 24-48 ساعة</p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-headset" data-setting="feature_3_icon"></i>
                        </div>
                        <h3 class="feature-title" data-setting="feature_3_title">دعم عملاء متميز</h3>
                        <p class="feature-description" data-setting="feature_3_description">فريق دعم متخصص ومدرب لمساعدتك في اختيار المنتجات المناسبة والإجابة على استفساراتك</p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-check" data-setting="feature_4_icon"></i>
                        </div>
                        <h3 class="feature-title" data-setting="feature_4_title">ضمان الجودة والأمان</h3>
                        <p class="feature-description" data-setting="feature_4_description">نظام دفع آمن ومشفر مع ضمان حماية كاملة لبياناتك الشخصية والمالية</p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-undo-alt" data-setting="feature_5_icon"></i>
                        </div>
                        <h3 class="feature-title" data-setting="feature_5_title">ضمان الاسترداد</h3>
                        <p class="feature-description" data-setting="feature_5_description">إمكانية إرجاع المنتج خلال 30 يوم مع ضمان استرداد كامل للمبلغ دون أي شروط معقدة</p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-user-md" data-setting="feature_6_icon"></i>
                        </div>
                        <h3 class="feature-title" data-setting="feature_6_title">استشارة مجانية</h3>
                        <p class="feature-description" data-setting="feature_6_description">استشارة مجانية من خبراء العناية المعتمدين لاختيار المنتجات الأنسب لنوع بشرتك وشعرك</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section class="section" data-section="about">
            <div class="container">
                <div class="section-header">
                    <div class="section-badge">
                        <i class="fas fa-heart"></i>
                        <span data-setting="about_badge">قصتنا</span>
                    </div>
                    <h2 class="section-title" data-setting="about_title">من نحن</h2>
                    <p class="section-subtitle" data-setting="about_subtitle">رحلتنا في عالم الجمال والعناية بدأت من شغف حقيقي بتقديم الأفضل لعملائنا الكرام في جميع أنحاء العراق</p>
                </div>

                <div style="max-width: 800px; margin: 0 auto; text-align: center; background: #ffffff; padding: 3rem; border-radius: 25px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border: 2px solid #4a90a4;">
                    <p style="font-size: 0.9rem; line-height: 1.8; color: #000000; margin-bottom: 2rem;" data-setting="about_description_1">
                        منذ تأسيسنا عام 2018، نسعى لتقديم أفضل منتجات العناية بالبشرة والشعر من أرقى العلامات التجارية العالمية. نؤمن بأن الجمال الحقيقي يبدأ من العناية الصحيحة والمنتجات عالية الجودة.
                    </p>
                    <p style="font-size: 0.9rem ; line-height: 1.8; color: #000000;" data-setting="about_description_2">
                        فريقنا المتخصص يعمل بجد لاختيار كل منتج بعناية فائقة، مع التأكد من أصالته وجودته العالية. نحن لسنا مجرد متجر، بل شريكك الموثوق في رحلة العناية والجمال.
                    </p>
                </div>

                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number" data-setting="stat_customers">12,500+</span>
                        <span class="stat-label">عميل سعيد</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-setting="stat_orders">25,000+</span>
                        <span class="stat-label">طلب مكتمل</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-setting="stat_years">7+</span>
                        <span class="stat-label">سنوات خبرة</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-setting="stat_rating">4.9</span>
                        <span class="stat-label">تقييم متوسط</span>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 4rem;">
                    <a href="contact.html" class="cta-button primary" style="margin-left: 1rem;">
                        <i class="fas fa-phone"></i>
                        تواصل معنا
                    </a>
                    <a href="products.html" class="cta-button secondary">
                        <i class="fas fa-shopping-bag"></i>
                        تسوق الآن
                    </a>
                </div>
            </div>
        </section>

        <!-- Product Categories Showcase Section -->
        <section class="categories-section" id="categories-section" style="display: none;">
            <div class="container">
                <div class="section-header">
                    <span class="section-badge" data-setting="categories_badge">تسوق حسب الفئة</span>
                    <h2 data-setting="categories_title">اكتشف مجموعاتنا المتنوعة</h2>
                    <p data-setting="categories_subtitle">تصفح مجموعة واسعة من منتجات العناية بالبشرة والشعر المصنفة بعناية لتناسب احتياجاتك</p>
                </div>

                <div class="categories-grid">
                    <div class="category-card">
                        <div class="category-image">
                            <img src="#" alt="العناية بالبشرة" data-setting="category_1_image">
                            <div class="category-overlay">
                                <div class="category-info">
                                    <h3 data-setting="category_1_name">العناية بالبشرة</h3>
                                    <p data-setting="category_1_description">منتجات متخصصة للعناية اليومية بالبشرة</p>
                                    <span class="category-count" data-setting="category_1_count">150+ منتج</span>
                                </div>
                                <a href="products.html?category=skincare" class="category-btn">
                                    <span>تسوق الآن</span>
                                    <i class="fas fa-arrow-left"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="category-card">
                        <div class="category-image">
                            <img src="#" alt="العناية بالشعر" data-setting="category_2_image">
                            <div class="category-overlay">
                                <div class="category-info">
                                    <h3 data-setting="category_2_name">العناية بالشعر</h3>
                                    <p data-setting="category_2_description">شامبو وبلسم وعلاجات للشعر</p>
                                    <span class="category-count" data-setting="category_2_count">120+ منتج</span>
                                </div>
                                <a href="products.html?category=haircare" class="category-btn">
                                    <span>تسوق الآن</span>
                                    <i class="fas fa-arrow-left"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="category-card">
                        <div class="category-image">
                            <img src="#" alt="المكياج" data-setting="category_3_image">
                            <div class="category-overlay">
                                <div class="category-info">
                                    <h3 data-setting="category_3_name">المكياج</h3>
                                    <p data-setting="category_3_description">مستحضرات تجميل عالية الجودة</p>
                                    <span class="category-count" data-setting="category_3_count">200+ منتج</span>
                                </div>
                                <a href="products.html?category=makeup" class="category-btn">
                                    <span>تسوق الآن</span>
                                    <i class="fas fa-arrow-left"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="category-card">
                        <div class="category-image">
                            <img src="#" alt="العطور" data-setting="category_4_image">
                            <div class="category-overlay">
                                <div class="category-info">
                                    <h3 data-setting="category_4_name">العطور</h3>
                                    <p data-setting="category_4_description">عطور فاخرة للرجال والنساء</p>
                                    <span class="category-count" data-setting="category_4_count">80+ منتج</span>
                                </div>
                                <a href="products.html?category=perfumes" class="category-btn">
                                    <span>تسوق الآن</span>
                                    <i class="fas fa-arrow-left"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 4rem;">
                    <a href="products.html" class="cta-button primary">
                        <i class="fas fa-th-large"></i>
                        عرض جميع الفئات
                    </a>
                </div>
            </div>
        </section>

        <!-- Customer Testimonials Section -->
        <section class="testimonials-section" id="testimonials-section" style="display: none;">
            <div class="container">
                <div class="section-header">
                    <span class="section-badge" data-setting="testimonials_badge">آراء عملائنا</span>
                    <h2 data-setting="testimonials_title">ماذا يقول عملاؤنا عنا؟</h2>
                    <p data-setting="testimonials_subtitle">تجارب حقيقية من عملائنا الكرام الذين جربوا منتجاتنا وخدماتنا المتميزة</p>
                </div>

                <div class="testimonials-grid">
                    <div class="testimonial-card">
                        <div class="testimonial-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="rating-text" data-setting="testimonial_1_rating">5.0</span>
                        </div>
                        <p class="testimonial-text" data-setting="testimonial_1_text">منتجات رائعة وجودة عالية، خاصة سيروم فيتامين سي. لاحظت تحسن كبير في بشرتي خلال أسبوعين فقط. التوصيل سريع والتعامل محترف جداً.</p>
                        <div class="testimonial-author">
                            <div class="author-avatar" data-setting="testimonial_1_avatar">
                                <span data-setting="testimonial_1_initial">س</span>
                            </div>
                            <div class="author-info">
                                <h4 data-setting="testimonial_1_name">سارة أحمد</h4>
                                <span data-setting="testimonial_1_location">بغداد</span>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card">
                        <div class="testimonial-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="rating-text" data-setting="testimonial_2_rating">5.0</span>
                        </div>
                        <p class="testimonial-text" data-setting="testimonial_2_text">أفضل متجر للعناية بالبشرة في العراق. المنتجات أصلية 100% والأسعار معقولة جداً. شامبو الكيراتين غير حياة شعري تماماً!</p>
                        <div class="testimonial-author">
                            <div class="author-avatar" data-setting="testimonial_2_avatar">
                                <span data-setting="testimonial_2_initial">م</span>
                            </div>
                            <div class="author-info">
                                <h4 data-setting="testimonial_2_name">مريم علي</h4>
                                <span data-setting="testimonial_2_location">البصرة</span>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card">
                        <div class="testimonial-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="rating-text" data-setting="testimonial_3_rating">5.0</span>
                        </div>
                        <p class="testimonial-text" data-setting="testimonial_3_text">خدمة عملاء ممتازة وتوصيل سريع. طلبت كريم مرطب وواقي شمس، وصلوا في نفس اليوم! جودة المنتجات فاقت توقعاتي.</p>
                        <div class="testimonial-author">
                            <div class="author-avatar" data-setting="testimonial_3_avatar">
                                <span data-setting="testimonial_3_initial">أ</span>
                            </div>
                            <div class="author-info">
                                <h4 data-setting="testimonial_3_name">أحمد محمد</h4>
                                <span data-setting="testimonial_3_location">أربيل</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="testimonials-stats">
                    <div class="stat-item">
                        <div class="stat-number" data-setting="testimonials_total_reviews">2,500+</div>
                        <div class="stat-label">تقييم إيجابي</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-setting="testimonials_average_rating">4.9</div>
                        <div class="stat-label">متوسط التقييم</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-setting="testimonials_satisfaction">98%</div>
                        <div class="stat-label">رضا العملاء</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Brand Story Timeline Section -->
        <section class="timeline-section" id="timeline-section" style="display: block;" data-section="timeline">
            <div class="container">
                <div class="section-header">
                    <span class="section-badge" data-setting="timeline_badge">رحلتنا</span>
                    <h2 data-setting="timeline_title">قصة نجاح متجر Care</h2>
                    <p data-setting="timeline_subtitle">رحلة من الشغف إلى التميز في عالم الجمال والعناية</p>
                </div>

                <div class="timeline-container">
                    <div class="timeline-line"></div>

                    <!-- Timeline Item 1 -->
                    <div class="timeline-item animate-on-scroll">
                        <div class="timeline-marker">
                            <i class="fas fa-lightbulb" data-setting="timeline_1_icon"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-year" data-setting="timeline_1_year">2018</div>
                            <h3 data-setting="timeline_1_title">بداية الحلم</h3>
                            <p data-setting="timeline_1_description">انطلقت فكرة متجر Care من شغف حقيقي بتقديم منتجات العناية الأصلية والعالية الجودة للعملاء في العراق</p>
                        </div>
                    </div>

                    <!-- Timeline Item 2 -->
                    <div class="timeline-item timeline-item-right animate-on-scroll">
                        <div class="timeline-marker">
                            <i class="fas fa-store" data-setting="timeline_2_icon"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-year" data-setting="timeline_2_year">2019</div>
                            <h3 data-setting="timeline_2_title">افتتاح المتجر</h3>
                            <p data-setting="timeline_2_description">افتتحنا أول متجر لنا في بغداد مع مجموعة مختارة من أفضل منتجات العناية بالبشرة والشعر</p>
                        </div>
                    </div>

                    <!-- Timeline Item 3 -->
                    <div class="timeline-item animate-on-scroll">
                        <div class="timeline-marker">
                            <i class="fas fa-users" data-setting="timeline_3_icon"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-year" data-setting="timeline_3_year">2020</div>
                            <h3 data-setting="timeline_3_title">نمو قاعدة العملاء</h3>
                            <p data-setting="timeline_3_description">وصلنا إلى أكثر من 1000 عميل راضٍ وبدأنا خدمة التوصيل لجميع محافظات العراق</p>
                        </div>
                    </div>

                    <!-- Timeline Item 4 -->
                    <div class="timeline-item timeline-item-right animate-on-scroll">
                        <div class="timeline-marker">
                            <i class="fas fa-globe" data-setting="timeline_4_icon"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-year" data-setting="timeline_4_year">2021</div>
                            <h3 data-setting="timeline_4_title">التوسع الرقمي</h3>
                            <p data-setting="timeline_4_description">أطلقنا متجرنا الإلكتروني لتسهيل عملية التسوق وتقديم تجربة أفضل لعملائنا</p>
                        </div>
                    </div>

                    <!-- Timeline Item 5 -->
                    <div class="timeline-item animate-on-scroll">
                        <div class="timeline-marker">
                            <i class="fas fa-award" data-setting="timeline_5_icon"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-year" data-setting="timeline_5_year">2023</div>
                            <h3 data-setting="timeline_5_title">التميز والجودة</h3>
                            <p data-setting="timeline_5_description">حصلنا على شهادات الجودة وأصبحنا الوجهة الأولى لمنتجات العناية في العراق</p>
                        </div>
                    </div>

                    <!-- Timeline Item 6 -->
                    <div class="timeline-item timeline-item-right animate-on-scroll">
                        <div class="timeline-marker">
                            <i class="fas fa-rocket" data-setting="timeline_6_icon"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-year" data-setting="timeline_6_year">2024</div>
                            <h3 data-setting="timeline_6_title">المستقبل المشرق</h3>
                            <p data-setting="timeline_6_description">نواصل رحلتنا نحو التميز مع خطط توسع جديدة وشراكات مع أفضل العلامات التجارية العالمية</p>
                        </div>
                    </div>
                </div>

                <!-- Timeline Stats -->
                <div class="timeline-stats animate-on-scroll">
                    <div class="timeline-stat animate-on-scroll">
                        <div class="stat-number" data-setting="timeline_stat_years">6+</div>
                        <div class="stat-label">سنوات من الخبرة</div>
                    </div>
                    <div class="timeline-stat animate-on-scroll">
                        <div class="stat-number" data-setting="timeline_stat_customers">5000+</div>
                        <div class="stat-label">عميل راضٍ</div>
                    </div>
                    <div class="timeline-stat animate-on-scroll">
                        <div class="stat-number" data-setting="timeline_stat_products">500+</div>
                        <div class="stat-label">منتج متنوع</div>
                    </div>
                    <div class="timeline-stat animate-on-scroll">
                        <div class="stat-number" data-setting="timeline_stat_brands">50+</div>
                        <div class="stat-label">علامة تجارية</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Interactive Product Benefits Showcase Section -->
        <section class="benefits-showcase-section" id="benefits-showcase-section" style="display: block;" data-section="benefits_showcase">
            <div class="container">
                <div class="section-header">
                    <span class="section-badge" data-setting="benefits_showcase_badge">فوائد منتجاتنا</span>
                    <h2 data-setting="benefits_showcase_title">اكتشف الفوائد المذهلة لمنتجاتنا</h2>
                    <p data-setting="benefits_showcase_subtitle">منتجات مصممة خصيصاً لتلبية احتياجاتك وتحقيق أفضل النتائج</p>
                </div>

                <div class="benefits-grid">
                    <!-- Benefit Item 1 -->
                    <div class="benefit-item animate-on-scroll" data-benefit="1">
                        <div class="benefit-icon">
                            <i class="fas fa-leaf" data-setting="benefit_1_icon"></i>
                        </div>
                        <div class="benefit-content">
                            <h3 data-setting="benefit_1_title">مكونات طبيعية</h3>
                            <p data-setting="benefit_1_description">مستخلصات طبيعية 100% آمنة على البشرة ومناسبة لجميع أنواع البشرة</p>
                        </div>
                        <div class="benefit-overlay">
                            <div class="benefit-details">
                                <h4>تفاصيل أكثر</h4>
                                <p data-setting="benefit_1_details">نستخدم أفضل المكونات الطبيعية المستخلصة من النباتات العضوية المعتمدة عالمياً</p>
                                <div class="benefit-stats">
                                    <span class="stat">
                                        <strong data-setting="benefit_1_stat_value">95%</strong>
                                        <small data-setting="benefit_1_stat_label">مكونات طبيعية</small>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Benefit Item 2 -->
                    <div class="benefit-item animate-on-scroll" data-benefit="2">
                        <div class="benefit-icon">
                            <i class="fas fa-shield-check" data-setting="benefit_2_icon"></i>
                        </div>
                        <div class="benefit-content">
                            <h3 data-setting="benefit_2_title">مختبر علمياً</h3>
                            <p data-setting="benefit_2_description">منتجات مختبرة في أفضل المختبرات العالمية لضمان الفعالية والأمان</p>
                        </div>
                        <div class="benefit-overlay">
                            <div class="benefit-details">
                                <h4>تفاصيل أكثر</h4>
                                <p data-setting="benefit_2_details">جميع منتجاتنا تخضع لاختبارات صارمة في مختبرات معتمدة دولياً</p>
                                <div class="benefit-stats">
                                    <span class="stat">
                                        <strong data-setting="benefit_2_stat_value">100%</strong>
                                        <small data-setting="benefit_2_stat_label">مختبر علمياً</small>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Benefit Item 3 -->
                    <div class="benefit-item animate-on-scroll" data-benefit="3">
                        <div class="benefit-icon">
                            <i class="fas fa-clock" data-setting="benefit_3_icon"></i>
                        </div>
                        <div class="benefit-content">
                            <h3 data-setting="benefit_3_title">نتائج سريعة</h3>
                            <p data-setting="benefit_3_description">نتائج ملحوظة خلال أسابيع قليلة من الاستخدام المنتظم</p>
                        </div>
                        <div class="benefit-overlay">
                            <div class="benefit-details">
                                <h4>تفاصيل أكثر</h4>
                                <p data-setting="benefit_3_details">تركيبات متقدمة تضمن امتصاص سريع وفعالية قصوى</p>
                                <div class="benefit-stats">
                                    <span class="stat">
                                        <strong data-setting="benefit_3_stat_value">2-4</strong>
                                        <small data-setting="benefit_3_stat_label">أسابيع للنتائج</small>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Benefit Item 4 -->
                    <div class="benefit-item animate-on-scroll" data-benefit="4">
                        <div class="benefit-icon">
                            <i class="fas fa-heart" data-setting="benefit_4_icon"></i>
                        </div>
                        <div class="benefit-content">
                            <h3 data-setting="benefit_4_title">مناسب للبشرة الحساسة</h3>
                            <p data-setting="benefit_4_description">تركيبات لطيفة وآمنة حتى للبشرة الأكثر حساسية</p>
                        </div>
                        <div class="benefit-overlay">
                            <div class="benefit-details">
                                <h4>تفاصيل أكثر</h4>
                                <p data-setting="benefit_4_details">خالي من المواد الكيميائية القاسية والعطور الصناعية</p>
                                <div class="benefit-stats">
                                    <span class="stat">
                                        <strong data-setting="benefit_4_stat_value">0%</strong>
                                        <small data-setting="benefit_4_stat_label">مواد ضارة</small>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Benefit Item 5 -->
                    <div class="benefit-item animate-on-scroll" data-benefit="5">
                        <div class="benefit-icon">
                            <i class="fas fa-award" data-setting="benefit_5_icon"></i>
                        </div>
                        <div class="benefit-content">
                            <h3 data-setting="benefit_5_title">حائز على جوائز</h3>
                            <p data-setting="benefit_5_description">منتجات حائزة على جوائز عالمية في مجال العناية والجمال</p>
                        </div>
                        <div class="benefit-overlay">
                            <div class="benefit-details">
                                <h4>تفاصيل أكثر</h4>
                                <p data-setting="benefit_5_details">معترف بها من قبل خبراء التجميل والعناية حول العالم</p>
                                <div class="benefit-stats">
                                    <span class="stat">
                                        <strong data-setting="benefit_5_stat_value">15+</strong>
                                        <small data-setting="benefit_5_stat_label">جائزة عالمية</small>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Benefit Item 6 -->
                    <div class="benefit-item animate-on-scroll" data-benefit="6">
                        <div class="benefit-icon">
                            <i class="fas fa-recycle" data-setting="benefit_6_icon"></i>
                        </div>
                        <div class="benefit-content">
                            <h3 data-setting="benefit_6_title">صديق للبيئة</h3>
                            <p data-setting="benefit_6_description">عبوات قابلة للتدوير ومكونات مستدامة بيئياً</p>
                        </div>
                        <div class="benefit-overlay">
                            <div class="benefit-details">
                                <h4>تفاصيل أكثر</h4>
                                <p data-setting="benefit_6_details">ملتزمون بحماية البيئة من خلال ممارسات مستدامة</p>
                                <div class="benefit-stats">
                                    <span class="stat">
                                        <strong data-setting="benefit_6_stat_value">100%</strong>
                                        <small data-setting="benefit_6_stat_label">قابل للتدوير</small>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Benefits Summary -->
                <div class="benefits-summary animate-on-scroll">
                    <div class="summary-content">
                        <h3 data-setting="benefits_summary_title">لماذا تختار منتجاتنا؟</h3>
                        <p data-setting="benefits_summary_description">نحن نؤمن بأن الجمال الحقيقي يأتي من منتجات عالية الجودة وآمنة. لذلك نقدم لك مجموعة منتجات مختارة بعناية لتحقيق أفضل النتائج.</p>
                        <div class="summary-stats">
                            <div class="summary-stat">
                                <span class="stat-number" data-setting="benefits_summary_stat1_value">98%</span>
                                <span class="stat-label" data-setting="benefits_summary_stat1_label">رضا العملاء</span>
                            </div>
                            <div class="summary-stat">
                                <span class="stat-number" data-setting="benefits_summary_stat2_value">50+</span>
                                <span class="stat-label" data-setting="benefits_summary_stat2_label">منتج متميز</span>
                            </div>
                            <div class="summary-stat">
                                <span class="stat-number" data-setting="benefits_summary_stat3_value">5</span>
                                <span class="stat-label" data-setting="benefits_summary_stat3_label">سنوات خبرة</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Newsletter Subscription Section -->
        <section class="newsletter-section" id="newsletter-section" style="display: none;">
            <div class="container">
                <div class="newsletter-content">
                    <div class="newsletter-text">
                        <h2 data-setting="newsletter_title">اشترك في نشرتنا الإخبارية</h2>
                        <p data-setting="newsletter_subtitle">احصل على أحدث العروض والنصائح للعناية بالبشرة والشعر مباشرة في بريدك الإلكتروني</p>

                        <div class="newsletter-benefits">
                            <div class="benefit-item">
                                <i class="fas fa-gift"></i>
                                <span data-setting="newsletter_benefit_1">عروض حصرية للمشتركين</span>
                            </div>
                            <div class="benefit-item">
                                <i class="fas fa-lightbulb"></i>
                                <span data-setting="newsletter_benefit_2">نصائح جمالية أسبوعية</span>
                            </div>
                            <div class="benefit-item">
                                <i class="fas fa-star"></i>
                                <span data-setting="newsletter_benefit_3">أول من يعرف المنتجات الجديدة</span>
                            </div>
                        </div>
                    </div>

                    <div class="newsletter-form-container">
                        <form class="newsletter-form" id="newsletterForm" action="https://formspree.io/f/xdkzqrez" method="POST">
                            <div class="form-group">
                                <input type="email" id="newsletterEmail" name="email" placeholder="أدخل بريدك الإلكتروني" required>
                                <input type="hidden" name="_subject" value="اشتراك جديد في النشرة الإخبارية - متجر Care">
                                <input type="hidden" name="_next" value="">
                                <input type="hidden" name="_language" value="ar">
                                <button type="submit" class="newsletter-btn">
                                    <span>اشتراك</span>
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                            <div class="newsletter-privacy">
                                <small data-setting="newsletter_privacy_text">نحن نحترم خصوصيتك ولن نشارك بريدك الإلكتروني مع أي طرف ثالث</small>
                            </div>
                        </form>

                        <div class="newsletter-success" id="newsletterSuccess" style="display: none;">
                            <div class="success-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <h3>تم الاشتراك بنجاح!</h3>
                            <p>شكراً لك على الاشتراك في نشرتنا الإخبارية. ستصلك أحدث العروض والنصائح قريباً.</p>
                        </div>
                    </div>
                </div>

                <div class="newsletter-stats">
                    <div class="stat-item">
                        <div class="stat-number" data-setting="newsletter_subscribers_count">5,000+</div>
                        <div class="stat-label">مشترك نشط</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-setting="newsletter_satisfaction_rate">95%</div>
                        <div class="stat-label">رضا المشتركين</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" data-setting="newsletter_weekly_tips">3</div>
                        <div class="stat-label">نصائح أسبوعية</div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>معلومات التواصل</h3>
                    <p><i class="fas fa-map-marker-alt"></i> <span class="business-address" data-setting="business_address">الكرادة، قرب مطعم المحطة</span></p>
                    <p><i class="fas fa-phone"></i> <span class="business-phone" data-setting="business_phone">***********</span></p>
                    <p><i class="fas fa-envelope"></i> <span class="business-email" data-setting="business_email"><EMAIL></span></p>
                </div>
                <div class="footer-section">
                    <h3>أوقات العمل</h3>
                    <p><span class="working-days" data-setting="working_days">السبت - الخميس</span>: <span class="working-hours" data-setting="working_hours">10 صباحاً - 5 مساءً</span></p>
                    <p><span class="closed-day" data-setting="closed_day">الجمعة</span>: مغلق</p>
                </div>
                <div class="footer-section">
                    <h3>تابعنا على وسائل التواصل</h3>
                    <div class="social-links">
                        <a href="#" class="whatsapp-link" target="_blank" title="واتساب" style="color: #25d366;" aria-label="تواصل معنا عبر واتساب">
                            <i class="fab fa-whatsapp" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="facebook-link" target="_blank" title="فيسبوك" style="color: #1877f2; display: none;" aria-label="تابعنا على فيسبوك">
                            <i class="fab fa-facebook-f" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="instagram-link" target="_blank" title="إنستغرام" style="color: #e4405f; display: none;" aria-label="تابعنا على إنستغرام">
                            <i class="fab fa-instagram" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="twitter-link" target="_blank" title="تويتر" style="color: #1da1f2; display: none;" aria-label="تابعنا على تويتر">
                            <i class="fab fa-twitter" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="telegram-link" target="_blank" title="تيليجرام" style="color: #0088cc; display: none;" aria-label="تابعنا على تيليجرام">
                            <i class="fab fa-telegram-plane" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="linkedin-link" target="_blank" title="لينكد إن" style="color: #0077b5; display: none;" aria-label="تابعنا على لينكد إن">
                            <i class="fab fa-linkedin-in" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="tiktok-link" target="_blank" title="تيك توك" style="color: #ff0050; display: none;" aria-label="تابعنا على تيك توك">
                            <i class="fab fa-tiktok" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="youtube-link" target="_blank" title="يوتيوب" style="color: #ff0000; display: none;" aria-label="تابعنا على يوتيوب">
                            <i class="fab fa-youtube" aria-hidden="true"></i>
                        </a>
                        <a href="#" class="snapchat-link" target="_blank" title="سناب شات" style="color: #fffc00; display: none;" aria-label="تابعنا على سناب شات">
                            <i class="fab fa-snapchat-ghost" aria-hidden="true"></i>
                        </a>
                    </div>
                    <div style="margin-top: 1.5rem;">
                        <p><a href="terms.html"><i class="fas fa-file-contract"></i>الشروط والأحكام</a></p>
                        <p><a href="terms.html"><i class="fas fa-shield-alt"></i>سياسة الخصوصية</a></p>
                        <p><a href="terms.html"><i class="fas fa-undo-alt"></i>سياسة الإرجاع</a></p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <span class="business-name" data-setting="business_name">Care</span>. <span class="copyright-text" data-setting="copyright_text">جميع الحقوق محفوظة</span>.</p>
            </div>
        </div>
    </footer>

    <!-- Shared Supabase Configuration (must load first) -->
    <script src="js/supabase-config.js"></script>

    <!-- Site Settings Script -->
    <script src="js/site-settings.js"></script>

    <script>


        // Cart functionality
        let cart = JSON.parse(localStorage.getItem('cart')) || [];

        function updateCartCount() {
            const cartCount = document.getElementById('cartCount');
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            cartCount.textContent = totalItems;

            if (totalItems > 0) {
                cartCount.style.animation = 'pulse 0.5s ease';
                setTimeout(() => {
                    cartCount.style.animation = '';
                }, 500);
            }
        }

        function addToCart(product) {
            const existingItem = cart.find(item => item.id === product.id);

            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    id: product.id,
                    name: product.name,
                    price: product.offer_price || product.price,
                    image_url: product.image_url,
                    quantity: 1
                });
            }

            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartCount();

            // Show notification
            const quantity = existingItem ? existingItem.quantity : 1;
            showCartNotification(product.name, quantity);
        }

        // Notification System
        function showCartNotification(productName, quantity) {
            // Remove any existing notifications
            const existingNotification = document.querySelector('.cart-notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            // Create notification element
            const notification = document.createElement('div');
            notification.className = 'cart-notification';
            notification.setAttribute('role', 'alert');
            notification.setAttribute('aria-live', 'polite');

            notification.innerHTML = `
                <div class="notification-card">
                    <div class="notification-icon">
                        <i class="fas fa-check" aria-hidden="true"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">تمت الإضافة بنجاح!</div>
                        <div class="notification-message">
                            تم إضافة "${productName}" إلى السلة
                            ${quantity > 1 ? `(الكمية: ${quantity})` : ''}
                        </div>
                        <div class="notification-actions">
                            <a href="cart.html" class="notification-btn primary">
                                <i class="fas fa-shopping-cart"></i>
                                عرض السلة
                            </a>
                            <button class="notification-btn secondary" onclick="closeCartNotification()">
                                <i class="fas fa-shopping-bag"></i>
                                متابعة التسوق
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(notification);

            // Trigger show animation
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);

            // Auto close after 4 seconds
            setTimeout(() => {
                closeCartNotification();
            }, 4000);
        }

        function closeCartNotification() {
            const notification = document.querySelector('.cart-notification');
            if (notification) {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentElement) {
                        document.body.removeChild(notification);
                    }
                }, 400);
            }
        }

        function formatPrice(price) {
            return new Intl.NumberFormat('ar-IQ', {
                style: 'decimal',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(price) + ' د.ع';
        }

        // Get shared Supabase client
        function getSupabaseClient() {
            // Use shared configuration (singleton pattern)
            if (window.SupabaseConfig) {
                return window.SupabaseConfig.getClient();
            }

            // Fallback to global client
            if (window.globalSupabaseClient) {
                return window.globalSupabaseClient;
            }

            // Last resort fallback
            if (window.supabase && typeof window.supabase.createClient === 'function') {
                console.warn('⚠️ Creating Supabase client directly in index.html - shared config may not be loaded');
                const SUPABASE_URL = 'https://krqijjttwllohulmdwgs.supabase.co';
                const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWlqanR0d2xsb2h1bG1kd2dzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDM4NTEsImV4cCI6MjA2NDM3OTg1MX0.E35EsJby1Y23hnTkwHt3lREAfH-nNKNt4PZtct5QI70';
                window.globalSupabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                return window.globalSupabaseClient;
            }

            return null;
        }

        async function loadFeaturedProducts() {
            const container = document.getElementById('featuredProducts');
            const loading = document.getElementById('loading');

            if (!container || !loading) return;

            try {
                const supabase = getSupabaseClient();
                if (!supabase) {
                    loading.style.display = 'none';
                    container.innerHTML = `
                        <div style="text-align: center; padding: 2rem; color: #e74c3c;">
                            <i class="fas fa-exclamation-triangle" style="font-size: var(--font-size-3xl); margin-bottom: 1rem;"></i>
                            <h3>خطأ في الاتصال بقاعدة البيانات</h3>
                            <p>يرجى إعادة تحميل الصفحة</p>
                        </div>
                    `;
                    return;
                }

                const response = await supabase
                    .from('products')
                    .select('*')
                    .eq('is_active', true)
                    .eq('is_featured', true)
                    .limit(8);

                if (response.error) {
                    throw response.error;
                }

                loading.style.display = 'none';

                if (response.data && response.data.length > 0) {
                    container.innerHTML = response.data.map((product, index) => {
                        const hasOffer = product.offer_price && product.offer_price < product.price;
                        const discountPercentage = hasOffer ? Math.round(((product.price - product.offer_price) / product.price) * 100) : 0;

                        let badges = [];
                        if (hasOffer) {
                            badges.push(`<div class="product-badge discount">خصم ${discountPercentage}%</div>`);
                        }
                        if (product.is_featured) {
                            badges.push(`<div class="product-badge featured">مميز</div>`);
                        }

                        // Get the first available image from any of the three image fields
                        const firstImage = product.image_url || product.image_url_2 || product.image_url_3;

                        return `
                        <article class="product-card ${!product.is_available ? 'out-of-stock' : ''}"
                                 onclick="window.location.href='product-details.html?id=${product.id}'"
                                 role="gridcell"
                                 aria-label="منتج ${product.name}">
                            <div class="product-image">
                                ${firstImage ?
                                    `<img src="${firstImage}"
                                         alt="${product.name}"
                                         loading="lazy">` :
                                    '<i class="fas fa-image" aria-hidden="true"></i>'
                                }
                                ${badges.length > 0 ? `<div class="product-badges">${badges.join('')}</div>` : ''}
                            </div>
                            <div class="product-info">
                                <h3 class="product-name">${product.name}</h3>
                                <div class="product-price">
                                    <span class="current-price">${formatPrice(product.offer_price || product.price)}</span>
                                    ${hasOffer ? `<span class="offer-price">${formatPrice(product.price)}</span>` : ''}
                                </div>
                                <button class="add-to-cart"
                                        onclick="event.stopPropagation(); addToCart(${JSON.stringify(product).replace(/"/g, '&quot;')})"
                                        ${!product.is_available ? 'disabled' : ''}
                                        aria-label="${product.is_available ? 'إضافة ' + product.name + ' إلى السلة' : 'المنتج غير متوفر'}">
                                    <i class="fas ${product.is_available ? 'fa-cart-plus' : 'fa-times'}" aria-hidden="true"></i>
                                    ${product.is_available ? 'إضافة للسلة' : 'غير متوفر'}
                                </button>
                            </div>
                        </article>
                    `;
                    }).join('');
                } else {
                    container.innerHTML = '<p style="text-align: center; color: #666; grid-column: 1 / -1;">لا توجد منتجات مميزة حالياً</p>';
                }
            } catch (error) {
                loading.style.display = 'none';
                container.innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: #e74c3c; grid-column: 1 / -1;">
                        <i class="fas fa-exclamation-triangle" style="font-size: var(--font-size-3xl); margin-bottom: 1rem;"></i>
                        <h3>خطأ في تحميل المنتجات المميزة</h3>
                        <p>${error.message}</p>
                        <button onclick="loadFeaturedProducts()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #4a90a4; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            إعادة المحاولة
                        </button>
                    </div>
                `;
            }
        }

        // Initialize page
        function initializePage() {
            updateCartCount();

            if (window.supabase && typeof window.supabase.createClient === 'function') {
                loadFeaturedProducts();
            } else {
                setTimeout(initializePage, 100);
            }
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize page components
            if (window.siteSettingsManager) {
                setTimeout(initializePage, 100);
            } else {
                window.addEventListener('siteSettingsLoaded', function() {
                    setTimeout(initializePage, 100);
                });

                setTimeout(() => {
                    initializePage();
                }, 3000);
            }

            // Listen for settings updates and reload content
            window.addEventListener('siteSettingsLoaded', function(e) {
                console.log('🔄 Site settings updated, refreshing content...');

                // Reload featured products if they exist
                if (typeof loadFeaturedProducts === 'function') {
                    setTimeout(loadFeaturedProducts, 500);
                }

                // Force a visual refresh
                document.body.style.opacity = '0.99';
                setTimeout(() => {
                    document.body.style.opacity = '1';
                }, 100);
            });

            // Listen for admin settings changes
            window.addEventListener('adminSettingsChanged', function(e) {
                console.log('🔄 Admin settings changed, refreshing page content...');
                location.reload();
            });

            // Cart icon keyboard support
            const cartIcon = document.querySelector('.cart-icon');
            cartIcon.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    window.location.href = 'cart.html';
                }
            });

            // Intersection Observer for animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animated');
                    }
                });
            }, observerOptions);

            // Observe elements for scroll animations
            document.querySelectorAll('.feature-card, .product-card, .stat-item, .section-header, .timeline-item, .timeline-stats, .timeline-stat, .benefit-item, .benefits-summary, .summary-content, .summary-stat').forEach(el => {
                if (!el.classList.contains('animate-on-scroll')) {
                    el.classList.add('animate-on-scroll');
                }
                observer.observe(el);
            });

            // Keyboard navigation support
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeCartNotification();
                }
            });
        });

        // Newsletter subscription functionality
        document.addEventListener('DOMContentLoaded', function() {
            const newsletterForm = document.getElementById('newsletterForm');
            const newsletterSuccess = document.getElementById('newsletterSuccess');

            if (newsletterForm) {
                newsletterForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const emailInput = document.getElementById('newsletterEmail');
                    const email = emailInput.value.trim();
                    const submitBtn = newsletterForm.querySelector('.newsletter-btn');

                    if (!email) {
                        showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
                        return;
                    }

                    // Validate email format
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(email)) {
                        showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
                        return;
                    }

                    // Show loading state
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
                    submitBtn.disabled = true;

                    try {
                        // Send to Formspree
                        const formData = new FormData(newsletterForm);

                        const response = await fetch('https://formspree.io/f/xdkzqrez', {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'Accept': 'application/json'
                            }
                        });

                        if (response.ok) {
                            // Show success message
                            newsletterForm.style.display = 'none';
                            newsletterSuccess.style.display = 'block';

                            showNotification('تم الاشتراك بنجاح! شكراً لك', 'success');

                            // Reset form after 5 seconds
                            setTimeout(() => {
                                newsletterForm.style.display = 'block';
                                newsletterSuccess.style.display = 'none';
                                emailInput.value = '';
                            }, 5000);
                        } else {
                            const data = await response.json();
                            if (data.errors) {
                                showNotification('حدث خطأ: ' + data.errors.map(error => error.message).join(', '), 'error');
                            } else {
                                showNotification('حدث خطأ أثناء الاشتراك. يرجى المحاولة مرة أخرى.', 'error');
                            }
                        }

                    } catch (error) {
                        console.error('Newsletter subscription error:', error);
                        showNotification('حدث خطأ أثناء الاشتراك. يرجى المحاولة مرة أخرى.', 'error');
                    } finally {
                        // Restore button state
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }
                });
            }
        });

        function showNotification(message, type = 'success') {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => notification.remove());

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}"></i>
                <span>${message}</span>
            `;

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                z-index: 10000;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-weight: 500;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;

            document.body.appendChild(notification);

            // Show notification
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 10);

            // Hide notification after 4 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 4000);
        }
    </script>
</body>
</html>